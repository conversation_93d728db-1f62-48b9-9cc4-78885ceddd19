<template>
  <a-modal
    v-model:open="modalVisible"
    title="嵌入详情"
    :width="900"
    :footer="null"
    @cancel="handleClose"
    class="embed-detail-modal"
  >
    <div class="modal-content">
      <!-- 服务条款说明 -->
      <div class="service-terms">
        使用本功能即表示您同意嵌入代码的相关条款。请确保您有权在目标网站上嵌入此代码，并遵守相关的服务条款、用户协议和隐私政策。
        <a href="#" class="link">《服务条款》</a>
        <a href="#" class="link">《用户协议》</a>
        <a href="#" class="link">《隐私条款》</a>
        等条款约束。
      </div>

      <!-- 确认嵌入网站信息 -->
      <div class="section">
        <div class="section-header">
          <span class="section-number">1</span>
          <span class="section-title">确认嵌入网站信息</span>
        </div>
        <div class="website-info-card">
          <div class="website-info">
            <div class="website-name">{{ props.websiteInfo.name }}</div>
            <div class="website-domain">已配置 {{ props.websiteInfo.domainCount }} 个域名</div>
          </div>
          <a-button type="text" size="small" @click="handleEditWebsite" class="edit-btn">
            <template #icon>
              <EditOutlined />
            </template>
          </a-button>
        </div>
      </div>

      <!-- 选择嵌入类型 -->
      <div class="section">
        <div class="section-header">
          <span class="section-number">2</span>
          <span class="section-title">选择嵌入类型</span>
        </div>
        <div class="section-subtitle">
          请根据您的需求选择合适的嵌入方式，不同方式适用于不同的网站场景
        </div>

        <!-- 类型选择器 -->
        <div class="embed-type-selection">
          <div
            class="embed-type-option"
            :class="{ active: selectedEmbedType === 'fullpage' }"
            @click="selectedEmbedType = 'fullpage'"
          >
            <div class="embed-type-title">全屏模式</div>
            <div class="embed-type-desc">
              适合独立页面嵌入，在网站的独立页面中完整展示 AI 助手功能，提供完整的交互体验
            </div>
            <div class="embed-type-preview">
              <!-- 预览占位符 -->
              <div class="preview-placeholder">
                <div class="preview-icon">🖥️</div>
                <div class="preview-text">全屏模式预览</div>
              </div>
            </div>
          </div>

          <div
            class="embed-type-option"
            :class="{ active: selectedEmbedType === 'chatbubble' }"
            @click="selectedEmbedType = 'chatbubble'"
          >
            <div class="embed-type-title">对话气泡</div>
            <div class="embed-type-desc">
              以悬浮气泡的形式嵌入，不影响原有页面布局，用户可以随时打开或关闭对话窗口
            </div>
            <div class="embed-type-preview">
              <!-- 预览占位符 -->
              <div class="preview-placeholder">
                <div class="preview-icon">💬</div>
                <div class="preview-text">对话气泡预览</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 代码展示区域 -->
      <div class="code-section">
        <div class="code-header">
          <span class="code-title">复制以下 JavaScript 嵌入代码到您的网站</span>
          <a-button type="primary" size="small" @click="handleCopyCode" class="copy-btn">
            <template #icon>
              <CopyOutlined />
            </template>
            复制
          </a-button>
        </div>
        <div class="code-container">
          <pre class="code-content">{{ generatedCode }}</pre>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { EditOutlined, CopyOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

// 定义 props
interface Props {
  visible: boolean;
  websiteInfo: {
    name: string;
    domainCount: number;
  };
}

const props = defineProps<Props>();

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  close: [];
}>();

// 响应式变量
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 选中的嵌入类型
const selectedEmbedType = ref<'fullpage' | 'chatbubble'>('fullpage');

// 生成的代码 - 使用模板字符串避免 Vue 编译器冲突
const generatedCode = computed(() => {
  const scriptTag = 'script';
  return `<!-- 以下代码嵌入到前端 html 文件的 body 内 -->
<${scriptTag} src="https://api-dev-platform-web.bj.bcebos.com/ai_apaas/embed/output/embedFullSDK.js?responseExpires=1722326400&authorization=bce-auth-v1%2F..."></${scriptTag}>
<${scriptTag}>
    new EmbedWebSDK({appId: 'edb4af4d-7a00-4ccd-ab68-d809d0b70461', code: 'embed1M5Gbw0h3cVsYeoPsQ84', render: '${selectedEmbedType.value}'});
</${scriptTag}>`;
});

// 关闭弹窗
const handleClose = () => {
  emit('close');
};

// 复制代码
const handleCopyCode = async () => {
  try {
    await navigator.clipboard.writeText(generatedCode.value);
    message.success('代码已复制到剪贴板');
  } catch (error) {
    message.error('复制失败，请手动复制');
  }
};

// 编辑网站信息
const handleEditWebsite = () => {
  message.info('编辑网站信息功能待开发');
};
</script>

<style scoped>
.embed-detail-modal :deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.embed-detail-modal :deep(.ant-modal-body) {
  padding: 24px;
}

.modal-content {
  font-size: 14px;
  line-height: 1.5;
}

/* 服务条款说明 */
.service-terms {
  color: #666;
  line-height: 1.6;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.service-terms .link {
  color: #1890ff;
  text-decoration: none;
  margin: 0 2px;
}

.service-terms .link:hover {
  text-decoration: underline;
}

/* 章节样式 */
.section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.section-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.section-subtitle {
  color: #666;
  font-size: 12px;
  margin-bottom: 16px;
}

/* 网站信息卡片 */
.website-info-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.website-info {
  flex: 1;
}

.website-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.website-domain {
  font-size: 12px;
  color: #8c8c8c;
}

.edit-btn {
  color: #666;
  padding: 4px;
}

.edit-btn:hover {
  color: #1890ff;
}

/* 嵌入类型选择 */
.embed-type-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.embed-type-option {
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.embed-type-option:hover {
  border-color: #1890ff;
}

.embed-type-option.active {
  border-color: #1890ff;
  background: #f6f9ff;
}

.embed-type-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 8px;
}

.embed-type-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16px;
}

.embed-type-preview {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
}

.preview-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.preview-text {
  font-size: 12px;
  color: #999;
}

/* 代码展示区域 */
.code-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.code-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.copy-btn {
  height: 28px;
  font-size: 12px;
}

.code-container {
  background: #2d3748;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
}

.code-content {
  color: #e2e8f0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
 
                     < s p a n   c l a s s = " s e c t i o n - n u m b e r " > 2 < / s p a n > 
 
                     < s p a n   c l a s s = " s e c t i o n - t i t l e " > ���SL]eQ�Nx< / s p a n > 
 
                 < / d i v > 
 
                 < d i v   c l a s s = " s e c t i o n - s u b t i t l e " > 
 
                     �Y�g�NxaY"N1Y����S�e͑�eubv^͑�eL]eQQ�z
 
                 < / d i v > 
 
 
 
                 < ! - -   �Nx	��b:S�W  - - > 
 
                 < d i v   c l a s s = " e m b e d - t y p e - s e l e c t i o n " > 
 
                     < d i v 
 
                         c l a s s = " e m b e d - t y p e - o p t i o n " 
 
                         : c l a s s = " {   a c t i v e :   s e l e c t e d E m b e d T y p e   = = =   ' f u l l p a g e '   } " 
 
                         @ c l i c k = " s e l e c t e d E m b e d T y p e   =   ' f u l l p a g e ' " 
 
                     > 
 
                         < d i v   c l a s s = " e m b e d - t y p e - t i t l e " > hQu�b�< / d i v > 
 
                         < d i v   c l a s s = " e m b e d - t y p e - d e s c " > 
 
                             \zf��SOY�[teL]eQ0R�vhu�b�-N�SO���f�[teN�Sꁚ[INU\:y�Q�[
 
                         < / d i v > 
 
                         < d i v   c l a s s = " e m b e d - t y p e - p r e v i e w " > 
 
                             < ! - -   `SMO�VGr:S�W  - - > 
 
                             < d i v   c l a s s = " p r e v i e w - p l a c e h o l d e r " > 
 
                                 < d i v   c l a s s = " p r e v i e w - i c o n " > =���< / d i v > 
 
                                 < d i v   c l a s s = " p r e v i e w - t e x t " > hQu�b���ȉ< / d i v > 
 
                             < / d i v > 
 
                         < / d i v > 
 
                     < / d i v > 
 
 
 
                     < d i v 
 
                         c l a s s = " e m b e d - t y p e - o p t i o n " 
 
                         : c l a s s = " {   a c t i v e :   s e l e c t e d E m b e d T y p e   = = =   ' c h a t b u b b l e '   } " 
 
                         @ c l i c k = " s e l e c t e d E m b e d T y p e   =   ' c h a t b u b b l e ' " 
 
                     > 
 
                         < d i v   c l a s s = " e m b e d - t y p e - t i t l e " > J�)Yl�l< / d i v > 
 
                         < d i v   c l a s s = " e m b e d - t y p e - d e s c " > 
 
                             �N�[݋Fh�vb__L]eQ(Wu�b�
N��S�N���eu�b�vQ�N�Q�[�1uRbc'Y\
 
                         < / d i v > 
 
                         < d i v   c l a s s = " e m b e d - t y p e - p r e v i e w " > 
 
                             < ! - -   `SMO�VGr:S�W  - - > 
 
                             < d i v   c l a s s = " p r e v i e w - p l a c e h o l d e r " > 
 
                                 < d i v   c l a s s = " p r e v i e w - i c o n " > =ج�< / d i v > 
 
                                 < d i v   c l a s s = " p r e v i e w - t e x t " > J�)Yl�l��ȉ< / d i v > 
 
                             < / d i v > 
 
                         < / d i v > 
 
                     < / d i v > 
 
                 < / d i v > 
 
             < / d i v > 
 
 
 
             < ! - -   �NxU\:y:S�W  - - > 
 
             < d i v   c l a s s = " c o d e - s e c t i o n " > 
 
                 < d i v   c l a s s = " c o d e - h e a d e r " > 
 
                     < s p a n   c l a s s = " c o d e - t i t l e " > ��\N�e  J a v a S c r i p t   L]eQ�`�vQ�z< / s p a n > 
 
                     < a - b u t t o n   t y p e = " p r i m a r y "   s i z e = " s m a l l "   @ c l i c k = " h a n d l e C o p y C o d e "   c l a s s = " c o p y - b t n " > 
 
                         < t e m p l a t e   # i c o n > 
 
                             < C o p y O u t l i n e d   / > 
 
                         < / t e m p l a t e > 
 
                         
Y6R
 
                     < / a - b u t t o n > 
 
                 < / d i v > 
 
                 < d i v   c l a s s = " c o d e - c o n t a i n e r " > 
 
                     < p r e   c l a s s = " c o d e - c o n t e n t " > { {   g e n e r a t e d C o d e   } } < / p r e > 
 
                 < / d i v > 
 
             < / d i v > 
 
         < / d i v > 
 
     < / a - m o d a l > 
 
 < / t e m p l a t e > 
 
 
 
 < s t y l e   s c o p e d > 
 
 . e m b e d - d e t a i l - m o d a l   : d e e p ( . a n t - m o d a l - h e a d e r )   { 
 
     b o r d e r - b o t t o m :   1 p x   s o l i d   # f 0 f 0 f 0 ; 
 
     p a d d i n g :   1 6 p x   2 4 p x ; 
 
 } 
 
 
 
 . e m b e d - d e t a i l - m o d a l   : d e e p ( . a n t - m o d a l - b o d y )   { 
 
     p a d d i n g :   2 4 p x ; 
 
 } 
 
 
 
 . m o d a l - c o n t e n t   { 
 
     f o n t - s i z e :   1 4 p x ; 
 
     l i n e - h e i g h t :   1 . 5 ; 
 
 } 
 
 
 
 / *   
g�Rag>k�f  * / 
 
 . s e r v i c e - t e r m s   { 
 
     c o l o r :   # 6 6 6 ; 
 
     l i n e - h e i g h t :   1 . 6 ; 
 
     m a r g i n - b o t t o m :   2 4 p x ; 
 
     p a d d i n g :   1 6 p x ; 
 
     b a c k g r o u n d :   # f 8 f 9 f a ; 
 
     b o r d e r - r a d i u s :   6 p x ; 
 
 } 
 
 
 
 . s e r v i c e - t e r m s   . l i n k   { 
 
     c o l o r :   # 1 8 9 0 f f ; 
 
     t e x t - d e c o r a t i o n :   n o n e ; 
 
     m a r g i n :   0   2 p x ; 
 
 } 
 
 
 
 . s e r v i c e - t e r m s   . l i n k : h o v e r   { 
 
     t e x t - d e c o r a t i o n :   u n d e r l i n e ; 
 
 } 
 
 
 
 / *   �z��7h_  * / 
 
 . s e c t i o n   { 
 
     m a r g i n - b o t t o m :   3 2 p x ; 
 
 } 
 
 
 
 . s e c t i o n - h e a d e r   { 
 
     d i s p l a y :   f l e x ; 
 
     a l i g n - i t e m s :   c e n t e r ; 
 
     m a r g i n - b o t t o m :   1 6 p x ; 
 
 } 
 
 
 
 . s e c t i o n - n u m b e r   { 
 
     d i s p l a y :   i n l i n e - f l e x ; 
 
     a l i g n - i t e m s :   c e n t e r ; 
 
     j u s t i f y - c o n t e n t :   c e n t e r ; 
 
     w i d t h :   2 0 p x ; 
 
     h e i g h t :   2 0 p x ; 
 
     b a c k g r o u n d :   # 1 8 9 0 f f ; 
 
     c o l o r :   w h i t e ; 
 
     b o r d e r - r a d i u s :   5 0 % ; 
 
     f o n t - s i z e :   1 2 p x ; 
 
     f o n t - w e i g h t :   5 0 0 ; 
 
     m a r g i n - r i g h t :   8 p x ; 
 
 } 
 
 
 
 . s e c t i o n - t i t l e   { 
 
     f o n t - s i z e :   1 6 p x ; 
 
     f o n t - w e i g h t :   5 0 0 ; 
 
     c o l o r :   # 2 6 2 6 2 6 ; 
 
 } 
 
 
 
 . s e c t i o n - s u b t i t l e   { 
 
     c o l o r :   # 6 6 6 ; 
 
     f o n t - s i z e :   1 2 p x ; 
 
     m a r g i n - b o t t o m :   1 6 p x ; 
 
 } 
 
 
 
 / *   Q�z�Oo`aSGr  * / 
 
 . w e b s i t e - i n f o - c a r d   { 
 
     d i s p l a y :   f l e x ; 
 
     a l i g n - i t e m s :   c e n t e r ; 
 
     j u s t i f y - c o n t e n t :   s p a c e - b e t w e e n ; 
 
     p a d d i n g :   1 6 p x ; 
 
     b o r d e r :   1 p x   s o l i d   # e 8 e 8 e 8 ; 
 
     b o r d e r - r a d i u s :   6 p x ; 
 
     b a c k g r o u n d :   # f a f a f a ; 
 
 } 
 
 
 
 . w e b s i t e - i n f o   { 
 
     f l e x :   1 ; 
 
 } 
 
 
 
 . w e b s i t e - n a m e   { 
 
     f o n t - s i z e :   1 4 p x ; 
 
     f o n t - w e i g h t :   5 0 0 ; 
 
     c o l o r :   # 2 6 2 6 2 6 ; 
 
     m a r g i n - b o t t o m :   4 p x ; 
 
 } 
 
 
 
 . w e b s i t e - d o m a i n   { 
 
     f o n t - s i z e :   1 2 p x ; 
 
     c o l o r :   # 8 c 8 c 8 c ; 
 
 } 
 
 
 
 . e d i t - b t n   { 
 
     c o l o r :   # 6 6 6 ; 
 
     p a d d i n g :   4 p x ; 
 
 } 
 
 
 
 . e d i t - b t n : h o v e r   { 
 
     c o l o r :   # 1 8 9 0 f f ; 
 
 } 
 
 
 
 / *   L]eQ{|�W	��b  * / 
 
 . e m b e d - t y p e - s e l e c t i o n   { 
 
     d i s p l a y :   g r i d ; 
 
     g r i d - t e m p l a t e - c o l u m n s :   1 f r   1 f r ; 
 
     g a p :   1 6 p x ; 
 
     m a r g i n - b o t t o m :   2 4 p x ; 
 
 } 
 
 
 
 . e m b e d - t y p e - o p t i o n   { 
 
     b o r d e r :   2 p x   s o l i d   # e 8 e 8 e 8 ; 
 
     b o r d e r - r a d i u s :   8 p x ; 
 
     p a d d i n g :   1 6 p x ; 
 
     c u r s o r :   p o i n t e r ; 
 
     t r a n s i t i o n :   a l l   0 . 3 s ; 
 
 } 
 
 
 
 . e m b e d - t y p e - o p t i o n : h o v e r   { 
 
     b o r d e r - c o l o r :   # 1 8 9 0 f f ; 
 
 } 
 
 
 
 . e m b e d - t y p e - o p t i o n . a c t i v e   { 
 
     b o r d e r - c o l o r :   # 1 8 9 0 f f ; 
 
     b a c k g r o u n d :   # f 6 f 9 f f ; 
 
 } 
 
 
 
 . e m b e d - t y p e - t i t l e   { 
 
     f o n t - s i z e :   1 6 p x ; 
 
     f o n t - w e i g h t :   5 0 0 ; 
 
     c o l o r :   # 2 6 2 6 2 6 ; 
 
     m a r g i n - b o t t o m :   8 p x ; 
 
 } 
 
 
 
 . e m b e d - t y p e - d e s c   { 
 
     f o n t - s i z e :   1 2 p x ; 
 
     c o l o r :   # 6 6 6 ; 
 
     l i n e - h e i g h t :   1 . 4 ; 
 
     m a r g i n - b o t t o m :   1 6 p x ; 
 
 } 
 
 
 
 . e m b e d - t y p e - p r e v i e w   { 
 
     h e i g h t :   1 2 0 p x ; 
 
     d i s p l a y :   f l e x ; 
 
     a l i g n - i t e m s :   c e n t e r ; 
 
     j u s t i f y - c o n t e n t :   c e n t e r ; 
 
 } 
 
 
 
 . p r e v i e w - p l a c e h o l d e r   { 
 
     d i s p l a y :   f l e x ; 
 
     f l e x - d i r e c t i o n :   c o l u m n ; 
 
     a l i g n - i t e m s :   c e n t e r ; 
 
     j u s t i f y - c o n t e n t :   c e n t e r ; 
 
     w i d t h :   1 0 0 % ; 
 
     h e i g h t :   1 0 0 % ; 
 
     b a c k g r o u n d :   # f 5 f 5 f 5 ; 
 
     b o r d e r - r a d i u s :   4 p x ; 
 
     b o r d e r :   1 p x   d a s h e d   # d 9 d 9 d 9 ; 
 
 } 
 
 
 
 . p r e v i e w - i c o n   { 
 
     f o n t - s i z e :   2 4 p x ; 
 
     m a r g i n - b o t t o m :   8 p x ; 
 
 } 
 
 
 
 . p r e v i e w - t e x t   { 
 
     f o n t - s i z e :   1 2 p x ; 
 
     c o l o r :   # 9 9 9 ; 
 
 } 
 
 
 
 / *   �NxU\:y:S�W  * / 
 
 . c o d e - s e c t i o n   { 
 
     b o r d e r - t o p :   1 p x   s o l i d   # f 0 f 0 f 0 ; 
 
     p a d d i n g - t o p :   2 4 p x ; 
 
 } 
 
 
 
 . c o d e - h e a d e r   { 
 
     d i s p l a y :   f l e x ; 
 
     a l i g n - i t e m s :   c e n t e r ; 
 
     j u s t i f y - c o n t e n t :   s p a c e - b e t w e e n ; 
 
     m a r g i n - b o t t o m :   1 2 p x ; 
 
 } 
 
 
 
 . c o d e - t i t l e   { 
 
     f o n t - s i z e :   1 4 p x ; 
 
     f o n t - w e i g h t :   5 0 0 ; 
 
     c o l o r :   # 2 6 2 6 2 6 ; 
 
 } 
 
 
 
 . c o p y - b t n   { 
 
     h e i g h t :   2 8 p x ; 
 
     f o n t - s i z e :   1 2 p x ; 
 
 } 
 
 
 
 . c o d e - c o n t a i n e r   { 
 
     b a c k g r o u n d :   # 2 d 3 7 4 8 ; 
 
     b o r d e r - r a d i u s :   6 p x ; 
 
     p a d d i n g :   1 6 p x ; 
 
     o v e r f l o w - x :   a u t o ; 
 
 } 
 
 
 
 . c o d e - c o n t e n t   { 
 
     c o l o r :   # e 2 e 8 f 0 ; 
 
     f o n t - f a m i l y :   ' M o n a c o ' ,   ' M e n l o ' ,   ' U b u n t u   M o n o ' ,   m o n o s p a c e ; 
 
     f o n t - s i z e :   1 2 p x ; 
 
     l i n e - h e i g h t :   1 . 5 ; 
 
     m a r g i n :   0 ; 
 
     w h i t e - s p a c e :   p r e - w r a p ; 
 
     w o r d - b r e a k :   b r e a k - a l l ; 
 
 } 
 
 < / s t y l e > 
 
 