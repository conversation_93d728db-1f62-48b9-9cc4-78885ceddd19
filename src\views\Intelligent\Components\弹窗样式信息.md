# 弹窗样式信息

## 整体布局

### 弹窗容器
- **背景**: 白色背景，带有圆角边框
- **阴影**: 外围有灰色阴影效果
- **位置**: 居中显示
- **尺寸**: 大型弹窗，占据屏幕较大区域

### 头部区域
- **标题**: "嵌入详情" - 左对齐显示
- **关闭按钮**: 右上角 "×" 按钮，灰色

## 内容区域

### 第一部分：说明文字
- **内容**: 服务条款说明文字
- **样式**: 灰色文字，多行显示
- **链接**: 包含蓝色超链接文字（服务协议、用户协议、隐私协议等）

### 第二部分：确认嵌入网站信息
- **标题**: "1 确认嵌入网站信息" - 带有蓝色圆形数字标识
- **输入框**:
  - 标签：环球数科
  - 占位符：已配置 1 个域名
  - 右侧有编辑图标

### 第三部分：获取嵌入代码
- **标题**: "2 获取嵌入代码" - 带有蓝色圆形数字标识
- **副标题**: "如果代码意外丢失，请及时重新生成并重新嵌入网站"

#### 代码选择区域
**左侧选项 - 全页面**
- **标题**: "全页面"
- **描述**: "将应用以对话页面整体并嵌入到目标页面中，体验顺畅且直观显示更多内容"
- **预览框**:
  - 白色背景
  - 蓝色边框（选中状态）
  - 内部显示简化的页面布局图标
  - 底部有蓝色选中指示器

**右侧选项 - 聊天气泡**
- **标题**: "聊天气泡"
- **描述**: "以对话窗口的形式悬浮在页面上，可以灵活控制页面其他内容的展示和操作关闭"
- **预览框**:
  - 白色背景
  - 灰色边框（未选中状态）
  - 内部显示气泡对话框图标

### 第四部分：代码展示区域
- **背景**: 深灰色/黑色代码编辑器样式
- **标题**: "请将下方 JavaScript 嵌入您的网站"
- **复制按钮**: 右上角复制图标
- **代码内容**:
  - HTML script 标签
  - JavaScript 代码
  - 包含 EmbedWebSDK 相关配置

## 颜色方案
- **主色调**: 蓝色（#007bff 或类似）
- **背景色**: 白色 (#ffffff)
- **文字色**:
  - 主文字：深灰色/黑色
  - 辅助文字：中灰色
  - 链接文字：蓝色
- **边框色**: 浅灰色
- **代码区域**: 深灰色背景，白色文字

## 交互元素
- **选择框**: 支持单选，选中时显示蓝色边框和底部指示器
- **编辑图标**: 可点击编辑
- **复制按钮**: 可复制代码
- **关闭按钮**: 可关闭弹窗
- **链接**: 可点击跳转

## 布局特点
- **响应式设计**: 适配不同屏幕尺寸
- **分步骤展示**: 使用数字标识清晰的步骤流程
- **视觉层次**: 通过颜色、字体大小、间距营造清晰的信息层次
- **用户友好**: 提供预览图和详细说明，降低用户理解成本