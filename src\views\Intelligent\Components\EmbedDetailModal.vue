<template>
  <a-modal
    v-model:open="modalVisible"
    title="嵌入详情"
    :width="900"
    :footer="null"
    @cancel="handleClose"
    class="embed-detail-modal"
  >
    <div class="modal-content">
      <!-- 服务条款说明 -->
      <div class="service-terms">
        使用本功能即表示您同意嵌入代码的相关条款。请确保您有权在目标网站上嵌入此代码，并遵守相关的服务条款、用户协议和隐私政策。
        <a href="#" class="link">《服务条款》</a>
        <a href="#" class="link">《用户协议》</a>
        <a href="#" class="link">《隐私条款》</a>
        等条款约束。
      </div>

      <!-- 确认嵌入网站信息 -->
      <div class="section">
        <div class="section-header">
          <span class="section-number">1</span>
          <span class="section-title">确认嵌入网站信息</span>
        </div>
        <div class="website-info-card">
          <div class="website-info">
            <div class="website-name">{{ props.websiteInfo.name }}</div>
            <div class="website-domain">已配置 {{ props.websiteInfo.domainCount }} 个域名</div>
          </div>
          <a-button type="text" size="small" @click="handleEditWebsite" class="edit-btn">
            <template #icon>
              <EditOutlined />
            </template>
          </a-button>
        </div>
      </div>

      <!-- 选择嵌入类型 -->
      <div class="section">
        <div class="section-header">
          <span class="section-number">2</span>
          <span class="section-title">选择嵌入类型</span>
        </div>
        <div class="section-subtitle">
          请根据您的需求选择合适的嵌入方式，不同方式适用于不同的网站场景
        </div>

        <!-- 类型选择器 -->
        <div class="embed-type-selection">
          <div
            class="embed-type-option"
            :class="{ active: selectedEmbedType === 'fullpage' }"
            @click="selectedEmbedType = 'fullpage'"
          >
            <div class="embed-type-title">全屏模式</div>
            <div class="embed-type-desc">
              适合独立页面嵌入，在网站的独立页面中完整展示AI助手功能，提供完整的交互体验
            </div>
            <div class="embed-type-preview">
              <!-- 预览占位符 -->
              <div class="preview-placeholder">
                <div class="preview-icon">🖥️</div>
                <div class="preview-text">全屏模式预览</div>
              </div>
            </div>
          </div>

          <div
            class="embed-type-option"
            :class="{ active: selectedEmbedType === 'chatbubble' }"
            @click="selectedEmbedType = 'chatbubble'"
          >
            <div class="embed-type-title">对话气泡</div>
            <div class="embed-type-desc">
              以悬浮气泡的形式嵌入，不影响原有页面布局，用户可以随时打开或关闭对话窗口
            </div>
            <div class="embed-type-preview">
              <!-- 预览占位符 -->
              <div class="preview-placeholder">
                <div class="preview-icon">💬</div>
                <div class="preview-text">对话气泡预览</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 代码展示区域 -->
      <div class="code-section">
        <div class="code-header">
          <span class="code-title">复制以下 JavaScript 嵌入代码到您的网站</span>
          <a-button type="primary" size="small" @click="handleCopyCode" class="copy-btn">
            <template #icon>
              <CopyOutlined />
            </template>
            复制
          </a-button>
        </div>
        <div class="code-container">
          <pre class="code-content">{{ generatedCode }}</pre>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { EditOutlined, CopyOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

// 定义 props
interface Props {
  visible: boolean;
  websiteInfo: {
    name: string;
    domainCount: number;
  };
}

const props = defineProps<Props>();

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  close: [];
}>();

// 响应式变量
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 选中的嵌入类型
const selectedEmbedType = ref<'fullpage' | 'chatbubble'>('fullpage');

// 生成的代码 - 使用模板字符串避免Vue编译器冲突
const generatedCode = computed(() => {
  const scriptTag = 'script';
  return `<!-- 以下代码嵌入到前端html文件的body内 -->
<${scriptTag} src="https://api-dev-platform-web.bj.bcebos.com/ai_apaas/embed/output/embedFullSDK.js?responseExpires=1722326400&authorization=bce-auth-v1%2F..."></${scriptTag}>
<${scriptTag}>
    new EmbedWebSDK({appId: 'edb4af4d-7a00-4ccd-ab68-d809d0b70461', code: 'embed1M5Gbw0h3cVsYeoPsQ84', render: '${selectedEmbedType.value}'});
</${scriptTag}>`;
});

// 关闭弹窗
const handleClose = () => {
  emit('close');
};

// 复制代码
const handleCopyCode = async () => {
  try {
    await navigator.clipboard.writeText(generatedCode.value);
    message.success('代码已复制到剪贴板');
  } catch (error) {
    message.error('复制失败，请手动复制');
  }
};

// 编辑网站信息
const handleEditWebsite = () => {
  message.info('编辑网站信息功能待开发');
};
</script>

<style scoped>
.embed-detail-modal :deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.embed-detail-modal :deep(.ant-modal-body) {
  padding: 24px;
}

.modal-content {
  font-size: 14px;
  line-height: 1.5;
}

/* 服务条款说明 */
.service-terms {
  color: #666;
  line-height: 1.6;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.service-terms .link {
  color: #1890ff;
  text-decoration: none;
  margin: 0 2px;
}

.service-terms .link:hover {
  text-decoration: underline;
}

/* 章节样式 */
.section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.section-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.section-subtitle {
  color: #666;
  font-size: 12px;
  margin-bottom: 16px;
}

/* 网站信息卡片 */
.website-info-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.website-info {
  flex: 1;
}

.website-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.website-domain {
  font-size: 12px;
  color: #8c8c8c;
}

.edit-btn {
  color: #666;
  padding: 4px;
}

.edit-btn:hover {
  color: #1890ff;
}

/* 嵌入类型选择 */
.embed-type-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.embed-type-option {
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.embed-type-option:hover {
  border-color: #1890ff;
}

.embed-type-option.active {
  border-color: #1890ff;
  background: #f6f9ff;
}

.embed-type-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 8px;
}

.embed-type-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16px;
}

.embed-type-preview {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
}

.preview-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.preview-text {
  font-size: 12px;
  color: #999;
}

/* 代码展示区域 */
.code-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.code-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.copy-btn {
  height: 28px;
  font-size: 12px;
}

.code-container {
  background: #2d3748;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
}

.code-content {
  color: #e2e8f0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
